-- SOLUÇÃO RÁPIDA: Desabilitar temporariamente o trigger problemático
-- Execute no SQL Editor do Supabase Dashboard

-- 1. Desabilitar o trigger de criação automática de trial
ALTER TABLE auth.users DISABLE TRIGGER IF EXISTS create_trial_subscription_on_signup;

-- 2. Verificar se isso resolve o problema de signup
-- Teste criar um usuário após executar o comando acima

-- 3. Se o signup funcionar, o problema está na função create_trial_subscription_trigger
-- Vamos corrigir a função:

CREATE OR REPLACE FUNCTION public.create_trial_subscription_trigger()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  trial_start timestamptz;
  trial_end timestamptz;
BEGIN
  -- Definir período de trial de 7 dias
  trial_start := now();
  trial_end := now() + interval '7 days';
  
  -- Criar assinatura trial com tratamento de erro melhorado
  BEGIN
    INSERT INTO subscriptions (
      user_id,
      plan_type,
      status,
      current_period_start,
      current_period_end,
      stripe_subscription_id,
      stripe_customer_id
    ) VALUES (
      NEW.id,
      'free',
      'trialing',
      trial_start,
      trial_end,
      null,
      null
    ) ON CONFLICT (user_id) DO NOTHING;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log do erro mas não falha a criação do usuário
      RAISE WARNING 'Erro ao criar subscription para usuário %: %', NEW.id, SQLERRM;
  END;
  
  -- Criar entrada de tracking de AI trial usage com tratamento de erro
  BEGIN
    INSERT INTO ai_trial_usage (
      user_id,
      trial_start_date,
      trial_end_date,
      total_budget_requests
    ) VALUES (
      NEW.id,
      trial_start,
      trial_end,
      0
    ) ON CONFLICT (user_id) DO NOTHING;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log do erro mas não falha a criação do usuário
      RAISE WARNING 'Erro ao criar ai_trial_usage para usuário %: %', NEW.id, SQLERRM;
  END;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Em caso de qualquer erro, logar mas não falhar a criação do usuário
    RAISE WARNING 'Erro geral no trigger de trial para usuário %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$;

-- 4. Reabilitar o trigger com a função corrigida
ALTER TABLE auth.users ENABLE TRIGGER create_trial_subscription_on_signup;

-- 5. Verificar se há constraint problemática e corrigi-la
-- Remover constraint problemática se existir
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_status_key;

-- Criar constraint correta (apenas uma assinatura ativa por usuário)
DROP INDEX IF EXISTS idx_subscriptions_user_active;
CREATE UNIQUE INDEX idx_subscriptions_user_active
ON subscriptions (user_id) 
WHERE status IN ('active', 'trialing');
